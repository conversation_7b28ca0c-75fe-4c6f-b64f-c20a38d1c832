<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .log {
            font-size: 12px;
            color: #666;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>CORS Debug Test</h1>
    
    <div class="test-section">
        <h2>Console Logs</h2>
        <div id="consoleLog" class="log">
            <em>Console logs will appear here...</em>
        </div>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <div class="test-section">
        <h2>Test 1: Frontend Proxy Login</h2>
        <button onclick="testFrontendProxy()">Test Frontend Proxy</button>
        <div id="proxyResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Direct Backend Login (Should CORS)</h2>
        <button onclick="testDirectBackend()">Test Direct Backend</button>
        <div id="backendResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: CORS Preflight Check</h2>
        <button onclick="testCORSPreflight()">Test CORS Preflight</button>
        <div id="preflightResult"></div>
    </div>

    <script>
        // Capture all console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleLogDiv = document.getElementById('consoleLog');

        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}] ${type.toUpperCase()}:</strong> ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.style.marginBottom = '5px';
            
            consoleLogDiv.appendChild(logEntry);
            consoleLogDiv.scrollTop = consoleLogDiv.scrollHeight;
        }

        console.log = function(...args) {
            addToLog('log', ...args);
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            addToLog('error', ...args);
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            addToLog('warn', ...args);
            originalWarn.apply(console, args);
        };

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled Promise Rejection:', event.reason);
        });

        function clearLogs() {
            consoleLogDiv.innerHTML = '<em>Console logs cleared...</em>';
        }

        // Test 1: Frontend Proxy Login
        async function testFrontendProxy() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.innerHTML = '<p>Testing frontend proxy...</p>';
            
            console.log('🧪 Testing Frontend Proxy Login');
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                console.log('Frontend proxy response status:', response.status);
                
                const data = await response.json();
                console.log('Frontend proxy response data:', data);
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>${response.ok ? '✅' : '❌'} Frontend Proxy Test</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Success:</strong> ${data.success}</p>
                        <p><strong>Token:</strong> ${data.token ? 'Received' : 'Not received'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('Frontend proxy error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Frontend Proxy Failed</h3>
                        <p>Error: ${error.message}</p>
                        <p>This indicates a CORS or network issue</p>
                    </div>
                `;
            }
        }

        // Test 2: Direct Backend Login (Should cause CORS error)
        async function testDirectBackend() {
            const resultDiv = document.getElementById('backendResult');
            resultDiv.innerHTML = '<p>Testing direct backend (should CORS)...</p>';
            
            console.log('🧪 Testing Direct Backend Login (expecting CORS error)');
            
            try {
                const response = await fetch('http://localhost:16001/api/sites/1/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                console.log('Direct backend response status:', response.status);
                
                const data = await response.json();
                console.log('Direct backend response data:', data);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Direct Backend Worked (CORS is properly configured)</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Success:</strong> ${data.success}</p>
                        <p>This means CORS is working correctly!</p>
                    </div>
                `;
            } catch (error) {
                console.error('Direct backend error (expected CORS):', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Direct Backend Failed (Expected CORS Error)</h3>
                        <p>Error: ${error.message}</p>
                        <p>This is expected - it shows CORS is blocking cross-origin requests</p>
                        <p><strong>If you see this error, CORS is working as intended!</strong></p>
                    </div>
                `;
            }
        }

        // Test 3: CORS Preflight
        async function testCORSPreflight() {
            const resultDiv = document.getElementById('preflightResult');
            resultDiv.innerHTML = '<p>Testing CORS preflight...</p>';
            
            console.log('🧪 Testing CORS Preflight');
            
            try {
                const response = await fetch('http://localhost:16001/api/sites/1/admin/login', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization'
                    }
                });
                
                console.log('CORS preflight response status:', response.status);
                
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                console.log('CORS preflight headers:', headers);
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>${response.ok ? '✅' : '❌'} CORS Preflight</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <h4>Response Headers:</h4>
                        <pre>${JSON.stringify(headers, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('CORS preflight error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ CORS Preflight Failed</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Initialize
        console.log('🚀 CORS Debug Test initialized');
        console.log('Current origin:', window.location.origin);
        console.log('Expected backend:', 'http://localhost:16001');
        console.log('Expected frontend:', 'http://localhost:16000');
    </script>
</body>
</html>
